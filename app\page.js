// app/page.js
"use client";

import React from 'react';
import Head from 'next/head';
import TypingEffect from '../components/TypingEffect'; // 确保路径正确

export default function HomePage() {
  const messageToType = "$\\text{\\LaTeX}$ Typesetting, Slide Simplified";

  return (
    <>
      <Head>
        <title>LaTeX Typesetting</title>
        {/* 你可以添加其他 meta 标签，如果需要 */}
      </Head>
      <main
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center', // 水平居中
          justifyContent: 'center', // 垂直居中
          minHeight: '100vh', // 占满整个视口高度
          padding: '1rem', // 给内容一些内边距，防止紧贴边缘
          backgroundColor: '#f0f2f5', // 可选：一个柔和的背景色
        }}
      >
        <TypingEffect
          textToType={messageToType}
          speed={0.08} // 你可以调整打字速度
        />
        {/* 重要提示: 
          GSAP TextPlugin 会打出上面的字符串。
          要将 "$L\\LaTeX$" 这部分渲染为真正的 LaTeX 视觉效果, 
          你需要集成一个像 KaTeX 或 MathJax 这样的库，并在打字完成后
          调用该库来处理包含 LaTeX 代码的元素。
          例如，如果使用 KaTeX，你可能需要在 TypingEffect 组件内部或外部
          获取到打字完成后的文本，然后用 KaTeX.render() 处理。
        */}
      </main>
    </>
  );
}