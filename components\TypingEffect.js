// components/TypingEffect.js
"use client";

import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { TextPlugin } from 'gsap/TextPlugin';

if (typeof window !== "undefined") {
  gsap.registerPlugin(TextPlugin);
}

const TypingEffect = ({ textToType, speed = 0.05 }) => { // 移除了未使用的 cursorChar prop
  const textRef = useRef(null);
  const componentRef = useRef(null);

  // 处理文本着色的函数
  const applyTextColoring = (element, text) => {
    // 将文本分割并应用样式
    const parts = text.split(/(\$\\text\{\\LaTeX\}\$|Slide)/g);
    element.innerHTML = '';

    parts.forEach(part => {
      const span = document.createElement('span');
      if (part === '$\\text{\\LaTeX}$' || part === 'Slide') {
        span.style.color = 'rgb(18, 125, 230)'; // 蓝色
      }
      span.textContent = part;
      element.appendChild(span);
    });
  };

  // 自定义打字效果，支持实时着色
  const customTypingEffect = (element, text, duration) => {
    return new Promise((resolve) => {
      let currentIndex = 0;
      const totalChars = text.length;
      const interval = duration * 1000 / totalChars; // 转换为毫秒

      const typeChar = () => {
        if (currentIndex <= totalChars) {
          const currentText = text.substring(0, currentIndex);
          applyTextColoring(element, currentText);
          currentIndex++;

          if (currentIndex <= totalChars) {
            setTimeout(typeChar, interval);
          } else {
            resolve();
          }
        }
      };

      typeChar();
    });
  };

  useEffect(() => {
    const targetElement = textRef.current;
    if (!targetElement) return;

    const ctx = gsap.context(async () => {
      targetElement.innerHTML = ""; // 清空之前的内容

      // 使用自定义打字效果
      await customTypingEffect(targetElement, textToType, textToType.length * speed);

      // 光标闪烁效果
      gsap.fromTo(targetElement,
        { borderRightColor: 'rgba(0,0,0,0.75)' }, // 初始可见光标
        {
          borderRightColor: 'rgba(0,0,0,0)', // 变透明
          repeat: -1, // 无限重复
          duration: 0.53, // 闪烁速度 (0.53s 是常见的光标闪烁周期)
          ease: 'steps(1)',
          delay: 0.5 // 在打字结束后0.5秒开始闪烁
        });

    }, componentRef);

    return () => ctx.revert();
  }, [textToType, speed]);

  return (
    <div ref={componentRef} style={{ display: 'inline-block' }}>
      <span
        ref={textRef}
        style={{
          fontFamily: '"LINE Seed Sans", "LINE Seed", sans-serif', // 使用 LINE Seed Sans 字体
          fontSize: '2em',        // 调整字体大小
          lineHeight: '1.5',      // 确保行高足够
          whiteSpace: 'pre-wrap', // 允许文本正常换行（如果需要的话）
          borderRight: `3px solid transparent`, // 为光标预留空间，初始透明
          paddingRight: '2px',
        }}
      ></span>
    </div>
  );
};

export default TypingEffect;