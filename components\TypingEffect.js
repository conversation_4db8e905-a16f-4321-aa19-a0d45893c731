// components/TypingEffect.js
"use client";

import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { TextPlugin } from 'gsap/TextPlugin';

if (typeof window !== "undefined") {
  gsap.registerPlugin(TextPlugin);
}

const TypingEffect = ({ textToType, speed = 0.05 }) => { // 移除了未使用的 cursorChar prop
  const textRef = useRef(null);
  const componentRef = useRef(null);

  // 处理文本着色的函数
  const applyTextColoring = (element, text) => {
    // 将文本分割并应用样式
    const parts = text.split(/(\$\\text\{\\LaTeX\}\$|Slide)/g);
    element.innerHTML = '';

    parts.forEach(part => {
      const span = document.createElement('span');
      if (part === '$\\text{\\LaTeX}$' || part === 'Slide') {
        span.style.color = 'rgb(18, 125, 230)'; // 蓝色
      }
      span.textContent = part;
      element.appendChild(span);
    });
  };

  useEffect(() => {
    const targetElement = textRef.current;
    if (!targetElement) return;

    const ctx = gsap.context(() => {
      targetElement.textContent = ""; // 清空之前的内容

      const tl = gsap.timeline();
      tl.to(targetElement, {
        duration: textToType.length * speed,
        text: {
          value: textToType,
          delimiter: ""
        },
        ease: "none",
        onComplete: () => {
          // 打字完成后应用颜色样式
          applyTextColoring(targetElement, textToType);
        }
      });

      // 移除或简化光标逻辑，如果不需要自定义闪烁
      // 如果需要打完字后有个简单光标，可以保留 borderRight 和一个简单的闪烁
      // 或者完全移除光标相关的动画和样式
      tl.fromTo(targetElement,
        { borderRightColor: 'rgba(0,0,0,0.75)' }, // 初始可见光标
        {
          borderRightColor: 'rgba(0,0,0,0)', // 变透明
          repeat: -1, // 无限重复
          duration: 0.53, // 闪烁速度 (0.53s 是常见的光标闪烁周期)
          ease: 'steps(1)',
        }, ">0.5"); // 在打字结束后0.5秒开始闪烁

    }, componentRef);

    return () => ctx.revert();
  }, [textToType, speed]);

  return (
    <div ref={componentRef} style={{ display: 'inline-block' }}>
      <span
        ref={textRef}
        style={{
          fontFamily: '"LINE Seed Sans", "LINE Seed", sans-serif', // 使用 LINE Seed Sans 字体
          fontSize: '2em',        // 调整字体大小
          lineHeight: '1.5',      // 确保行高足够
          whiteSpace: 'pre-wrap', // 允许文本正常换行（如果需要的话）
          borderRight: `3px solid transparent`, // 为光标预留空间，初始透明
          paddingRight: '2px',
        }}
      ></span>
    </div>
  );
};

export default TypingEffect;